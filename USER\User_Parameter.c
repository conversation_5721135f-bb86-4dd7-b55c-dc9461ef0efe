#include "User_Parameter.h"
#include "User_IR_Sensor.h"

int Last_Error=0;		//上一次的误差
int Sum_Error=0;	   	//累计误差
int PID_Out=0;			//PID输出的值，这里放到10倍
int PID_P = 60;			//参数，P值（调小以防止过度转向）
int PID_I=0;			//参数，I值
int PID_D=0;			//参数，D值
int Speed=80;			//基准行驶速度


void Write_Parameter(void)
{
//	unsigned char mm;
//	EEPROM_write_Char(0x0000,0xAA);
//	for(mm=0;mm<6;mm++){ EEPROM_write_Int(0x0010+mm*2,ADC_Threshold[mm]);}
//	EEPROM_write_Int(0x0030,PID_P);			//参数，P值
//	EEPROM_write_Int(0x0032,PID_I);			//参数，I值
//	EEPROM_write_Int(0x0034,PID_D);			//参数，D值
//	EEPROM_write_Int(0x0036,Speed);			//基准行驶速度
}
void Read_Parameter(void)
{	
//	unsigned char mm;

//	if(KEY3==0)	
//	{
//		EEPROM_SectorErase(0x01);		//擦除地址0x01所在扇区的全部数据
//		Write_Parameter();				//手动，数据恢复出厂设置
//	}
//	if(	EEPROM_read_Char(0x0000)==0xFF)//第一次读取数据，EEPROM中无数据，需要写入参数
//	{
//		Write_Parameter();//数据恢复出厂设置	
//	}
//	for(mm=0;mm<6;mm++)
//	{
//		ADC_Threshold[mm]=EEPROM_read_Int(0x0010+mm*2);
//	}
//	PID_P=EEPROM_read_Int(0x0030);			//参数，P值
//	PID_I=EEPROM_read_Int(0x0032);			//参数，I值
//	PID_D=EEPROM_read_Int(0x0034);			//参数，D值
//	Speed=EEPROM_read_Int(0x0036);			//基准行驶速度		
}









