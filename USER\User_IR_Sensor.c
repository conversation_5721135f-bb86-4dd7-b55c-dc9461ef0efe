#include <User_IR_Sensor.h>
#include <led.h>
#define Threshold	 150
int Error=0;		 	//传感器检测轨道的偏差
unsigned short int IR_RES=0;
unsigned short int IR_RES_Original=0; // 保存原始值用于调试显示
int FLAG_left=0;
unsigned char ADC_Data[12]={0,0,0,0,0,0,0,0,0,0,0,0};  	//实时采样值
unsigned char ADC_Min[12]={240,240,240,240,240,240,240,240,240,240,240,240};	//采样通道的最小值
//unsigned char ADC_Threshold[12]={192,188,193,204,225,192,209,202,206,205,194,202};		//阈值
//unsigned char ADC_Threshold[12]={120,120,120,120,120,120,120,120,120,120,120,120};		//阈值
unsigned char ADC_Threshold[12]={Threshold,Threshold,<PERSON>hreshold,Threshold,Threshold,Threshold,Threshold,Threshold,Threshold,Threshold,Threshold,Threshold};		//阈值
unsigned char ADC_Max[12]={90,90,90,90,90,90,90,90,90,90,90,90};				//采样通道的最大值

unsigned char ADC_Flag=0;
unsigned char ADC1_Flag=0;

void ADC12_0_INST_IRQHandler(void                                                                      )
{

    if (DL_ADC12_getPendingInterrupt(ADC12_0_INST)==DL_ADC12_IIDX_MEM8_RESULT_LOADED) //等待中断序列0
    {			
        ADC_Data[3] = DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_0);
        ADC_Data[4] = DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_1);
        ADC_Data[5] = DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_2);
        ADC_Data[6] = DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_3);
        ADC_Data[7] = DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_4);
        ADC_Data[8] = DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_5);
//			
//        ADC_Data[2] = DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_6);
//        ADC_Data[1] = DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_7);
//        ADC_Data[0] = DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_8);
#ifdef IR_12CH
				ADC_Data[0] = DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_6);
        ADC_Data[1] = DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_7);
        ADC_Data[2] = DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_8);	
#endif			
        DL_ADC12_enableConversions(ADC12_0_INST);
				ADC_Flag=1;
    }
}
#ifdef IR_12CH
void ADC12_1_INST_IRQHandler(void)
{

    if (DL_ADC12_getPendingInterrupt(ADC12_1_INST)==DL_ADC12_IIDX_MEM2_RESULT_LOADED) //等待中断序列0
    {		
				ADC_Data[9] = DL_ADC12_getMemResult(ADC12_1_INST, DL_ADC12_MEM_IDX_0);
        ADC_Data[10] = DL_ADC12_getMemResult(ADC12_1_INST, DL_ADC12_MEM_IDX_1);
				ADC_Data[11] = DL_ADC12_getMemResult(ADC12_1_INST, DL_ADC12_MEM_IDX_2);
        DL_ADC12_enableConversions(ADC12_1_INST);
				ADC1_Flag=1;
    }
}
#endif

void ADC_Init(void)
{
    NVIC_EnableIRQ(ADC12_0_INST_INT_IRQN);
#ifdef IR_12CH
	NVIC_EnableIRQ(ADC12_1_INST_INT_IRQN);
#endif	
}

void ADC_start(void)
{
  DL_ADC12_startConversion(ADC12_0_INST);	//开始转换
#ifdef IR_12CH	
	DL_ADC12_startConversion(ADC12_1_INST);  //开始转换
#endif
}
// 


void Display_ADC(void)
{
	unsigned char nn;
	//ADC采样6个通道的数值
	for(nn=0;nn<6;nn++)
	{
		OLEDLCD_Put6x7Num(0,10+9*nn,ADC_Data[nn],3,1);//显示采样的值
	}
	
	for(nn=0;nn<6;nn++)
	{
		OLEDLCD_Put6x7Num(50,10+9*nn,ADC_Data[nn+6],3,1);//显示采样的值
	}
		
		
}
void Binarization_Display(unsigned char xx,unsigned char yy,unsigned char Sc)
{
	unsigned short int IR_Temp;
	unsigned char nn;
	
	IR_Temp=0x0004;
	for(nn=0;nn<6;nn++) //二值化6个通道的数值
	{
		if((IR_RES&IR_Temp)==IR_Temp)
			OLEDLCD_Put6x7ENstr(xx+nn*6,yy,"*",1);
		else 
			OLEDLCD_Put6x7ENstr(xx+nn*6,yy,"-",1);
		IR_Temp<<=1;
	}
	for(nn=6;nn<12;nn++) //二值化6个通道的数值
	{
		if((IR_RES&IR_Temp)==IR_Temp)
			OLEDLCD_Put6x7ENstr(xx+nn*6,yy,"*",1);
		else 
			OLEDLCD_Put6x7ENstr(xx+nn*6,yy,"-",1);
		IR_Temp<<=1;
	}	
	//显示偏差
	if(Sc==1)
	{
		OLEDLCD_Put6x12Num(90,2,Error,3,1);
		// 显示原始IR_RES值用于调试（16进制显示）
		OLEDLCD_Put6x7Num(90,15,IR_RES_Original,5,1);
	}
}
void Display_Threshold(void)
{
	unsigned char nn;
	for(nn=0;nn<6;nn++) //二值化6个通道的数值
	{
	  OLEDLCD_Put6x7Num(23,10+9*nn,ADC_Threshold[nn],3,1); //显示阈值
	}
	for(nn=6;nn<12;nn++) //二值化6个通道的数值
	{
	  OLEDLCD_Put6x7Num(73,10+9*(nn-6),ADC_Threshold[nn],3,1); //显示阈值
	}		
}
void ADC_Transform(void)
{
	unsigned short int Temp=0x2000;
	int L_x,R_x;
	int nn;
	IR_RES=0;//清零
	for(nn=0;nn<12;nn++) //二值化6个通道的数值
	{
		if(ADC_Data[nn]<ADC_Threshold[nn])IR_RES|=Temp;
		Temp>>=1;
	}

	// 保存原始IR_RES值用于Error计算
	IR_RES_Original = IR_RES;

	// 临时注释掉强制设置，用于调试
	// IR_RES |= 0x0008;  // 设置bit 3（传感器1）为1，表示检测到黑线

	//计算偏差量（使用原始值）
	switch(IR_RES_Original)
	{
//		case 0x40:	Error=5;break;
//		case 0x60:	Error=4;break;
//		case 0x20:	Error=3;break;
//		case 0x30:	Error=2;break;
//		case 0x10:	Error=1;break;
//		case 0x18:	Error=0;break;
//		case 0x08:	Error=-1;break;
//		case 0x0C:	Error=-2;break;
//		case 0x04:	Error=-3;break;
//		case 0x06:	Error=-4;break;
//		case 0x02:	Error=-5;break;

//		case 0x70:	Error=2;break;
//		case 0x38:	Error=1;break;
//		case 0x1C:	Error=-1;break;
//		case 0x0E:	Error=-2;break;

//		case 0x71:	Error=2;break;
//		case 0x3C:	Error=0;break;
//		case 0x1E:	Error=-2;break;
				      
        case 0x0004:Error=15;break;
				case 0x000C:Error=14;break;
				case 0x0008:Error=13;break;
				case 0x001C:Error=12;break;
				case 0x0018:Error=11;break;
				case 0x0010:Error=10;break;			
				case 0x0038:Error=9;break;
				case 0x0030:Error=8;break;
				case 0x0020:Error=7;break;
				case 0x0070:Error=6;break;
				case 0x0060:Error=5;break;
				case 0x0040:Error=4;break;
				case 0x00E0:Error=3;break;
				case 0x00C0:Error=2;break;
				case 0x0080:Error=1;break;
				case 0x0180:Error=0;break;
				
        case 0x0100:Error=-1;break;
        case 0x0300:Error=-2;break;
        case 0x0700:Error=-3;break;
        case 0x0200:Error=-4;break;
        case 0x0600:Error=-5;break;
        case 0x0E00:Error=-6;break;
        case 0x0400:Error=-7;break;
        case 0x0C00:Error=-8;break;
        case 0x1C00:Error=-9;break;
        case 0x0800:Error=-10;break;
        case 0x1800:Error=-11;break;
				case 0x3800:Error=-12;break;    //0011 1000 0000 0000
				case 0x1000:Error=-13;break;    
				case 0x3000:Error=-14;break;
				case 0x2000:Error=-15;break;         
				
				
				
				case 0x0000:break;//不处理，可以丢线保持
				
//				default:
//					Temp=0x2000;
//					for(nn=0;nn<12;nn++)
//					{
//						if(Temp&IR_RES)
//						{
//							L_x=nn;
//							break;
//						}	
//						Temp>>=1;
//					}
//					Temp=0x0004;
//					for(nn=0;nn<12;nn++)
//					{
//						if(Temp&IR_RES)
//						{
//							R_x=6;
//							break;
//						}	
//						Temp<<=1;
//					}		
//					if(L_x<R_x)	Error=-15;
//					else 				Error=15;
//						
//					break;
				
//				case 0x3C00:
//				case 0x1E00:
//				case 0x0F00:
//				case 0x1F00:
//				case 0x3E00:
//				case 0x3F00:Error=-14;break;
//				

//				case 0x0F80:
//				case 0x1F80:
//				case 0x3F10:Error=-13;break;
//				
//				case 0x0FC0:
//				case 0x1FC0:
//				case 0x3FC0:Error=-12;break;	
				
//        case 0x3F00:case 0x1F80:case 0x0FC0:case 0x07E0:case 0x03F0:case 0x01F8:case 0x00FC://连续6个传感器压线
//        case 0x3F10:case 0x1FC0:case 0x0FE0:case 0x07F0:case 0x03F8:case 0x01FC://连续7个传感器压线
//        case 0x3FC0:case 0x1F70:case 0x0FF0:case 0x07F8:case 0x03FC://连续8个传感器压线
//        case 0x3FE0:case 0x1FF0:case 0x0FF8:case 0x0EFC://连续9个传感器压线
//        case 0x3FF0:case 0x1FF8:case 0x0FFC://连续10个传感器压线
//        case 0x3FF8:case 0x1FFC://连续11个传感器压线
//        case 0x3FFC://连续12个传感器压线
//            if(StopLine_Flag<StopLine_Theshold) StopLine_Flag++;//标记停车线
//            //if(SuspendLine_Flag<SuspendLine_Theshold)SuspendLine_Flag=0;
//            break;
     /* case 0x3FC0:case 0x1F70:case 0x0FF0:case 0x07F8:case 0x03FC://连续8个传感器压线
        case 0x3FE0:case 0x1FF0:case 0x0FF8:case 0x0EFC://连续9个传感器压线
        case 0x3FF0:case 0x1FF8:case 0x0FFC://连续10个传感器压线
        case 0x3FF8:case 0x1FFC://连续11个传感器压线
        case 0x3FFC://连续12个传感器压线
            if(SuspendLine_Flag<SuspendLine_Theshold) SuspendLine_Flag++;
            if(StopLine_Flag<StopLine_Theshold)StopLine_Flag=0;
            break;
        case 0x0000:
            if(SuspendLine_Flag>=SuspendLine_Theshold)
            {
                if(SuspendWhiteLine_Flag>=SuspendWhiteLine_Theshold || StopLine_Flag>=StopLine_Theshold)
                    SuspendWhiteLine_Flag++;
                else
                    Suspend_CK_Flag=1;
            }
            break;*/
//        default:if(StopLine_Flag<StopLine_Theshold)StopLine_Flag=0;break;		
	}
}





